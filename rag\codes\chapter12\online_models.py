from typing import Dict, List, Union

from lazyllm import OnlineChatModule, OnlineEmbeddingModule


from lazyllm.module import OnlineEmbeddingModuleBase

class CustomOnlineEmbeddingModule(OnlineEmbeddingModuleBase):
    """CustomOnlineEmbeddingModule"""

    def __init__(self, embed_url, embed_model_name, api_key, model_series):
        super().__init__(
            embed_url=embed_url, embed_model_name=embed_model_name,
            api_key=api_key, model_series=model_series
        )

    def _encapsulated_data(self, text: str, **kwargs) -> Dict[str, str]:
        json_data = {"inputs": text, "model": self._embed_model_name}
        if len(kwargs) > 0:
            json_data.update(kwargs)

        return json_data

    def _parse_response(
            self,
            response: Union[List[List[str]], Dict]
        ) -> Union[List[List[str]], Dict]:
        return response


DOUBAO_API_KEY = ""
DEEPSEEK_API_KEY = ""
QWEN_API_KEY = ""

llm = OnlineChatModule(
    source="deepseek",
    api_key=DEEPSEEK_API_KEY,
)

embedding_model = OnlineEmbeddingModule(
    source="glm",
    embed_model_name="embedding-3",
    api_key="e37e81fae0e04aab9340b25a2f351e42.c34b4fosyCYIJKhb",
    embed_url="https://open.bigmodel.cn/api/paas/v4/embeddings/",  # 注意：末尾添加了斜杠
)

custom_embedding_model = CustomOnlineEmbeddingModule(
    embed_url="",
    embed_model_name="BAAI/bge-m3",
    api_key="",
    model_series="bge"
)

rerank_model = OnlineEmbeddingModule(
    source="qwen",
    api_key=QWEN_API_KEY,
    type="rerank"
)
